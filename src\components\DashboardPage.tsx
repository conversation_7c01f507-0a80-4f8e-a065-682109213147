import React, { useState, useEffect } from 'react';
import { Youtube, Instagram, ArrowUp, Link, Play, TrendingUp, Users, Video, Heart, MessageCircle, Eye, Zap, Target, Sparkles, Clock, Calendar, BarChart3, Activity, Star } from 'lucide-react';
import GrowthComparisonChart from './GrowthComparisonChart';

const DashboardPage: React.FC = () => {
  const [activeChartTab, setActiveChartTab] = useState('YouTube');
  const [dashboardLoadTime] = useState(new Date()); // Capture time when dashboard loads
  const [animatedNumbers, setAnimatedNumbers] = useState({
    youtube: 0,
    tiktok: 0,
    instagram: 0
  });

  // Animate numbers on mount
  useEffect(() => {
    const animateNumber = (target: number, key: keyof typeof animatedNumbers) => {
      let current = 0;
      const increment = target / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        setAnimatedNumbers(prev => ({ ...prev, [key]: Math.floor(current) }));
      }, 30);
    };

    animateNumber(145700, 'youtube');
    animateNumber(28600, 'tiktok');
    animateNumber(52400, 'instagram');
  }, []);

  // Enhanced data with more metrics
  const dashboardData = {
    date: dashboardLoadTime.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
    lastUpdated: dashboardLoadTime.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    }),
    youtube: {
      subscribers: '145.7K',
      subscribersRaw: 145700,
      growth: 7.2,
      videos: 87,
      totalViews: '2.1M',
      avgEngagement: 0.045,
      topVideo: 'Minecraft Speedrun Tips',
      recentGrowth: '+1.2K this week'
    },
    tiktok: {
      followers: '28.6K',
      followersRaw: 28600,
      growth: 3.8,
      posts: 124,
      totalViews: '890K',
      avgEngagement: 0.067,
      topVideo: 'Gaming Highlights',
      recentGrowth: '+890 this week'
    },
    instagram: {
      followers: '52.4K',
      followersRaw: 52400,
      growth: 6.5,
      posts: 158,
      totalViews: '1.3M',
      avgEngagement: 0.038,
      topVideo: 'Behind the Scenes',
      recentGrowth: '+2.1K this week'
    },
  };

  const quickStats = [
    { label: 'Total Reach', value: '4.29M', icon: Eye, color: 'text-blue-400', bg: 'bg-blue-500/10' },
    { label: 'Avg. Engagement', value: '0.050', icon: Heart, color: 'text-pink-400', bg: 'bg-pink-500/10' },
    { label: 'Content Pieces', value: '369', icon: Video, color: 'text-purple-400', bg: 'bg-purple-500/10' },
    { label: 'Growth Rate', value: '+5.8%', icon: TrendingUp, color: 'text-green-400', bg: 'bg-green-500/10' },
  ];



  return (
    <div className="flex flex-col p-8 text-white min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Animated Demo Banner */}
      <div className="bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 text-white text-center py-3 px-6 font-bold text-sm sm:text-base mb-8 rounded-xl shadow-lg animate-pulse">
        <div className="flex items-center justify-center gap-2">
          <Sparkles className="w-5 h-5 animate-spin" />
          🚧 This is a demo version - Experience the future of content analytics!
          <Sparkles className="w-5 h-5 animate-spin" />
        </div>
      </div>

      {/* Enhanced Header with Welcome Animation */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="relative">
              <h1 className="text-5xl font-bold bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent animate-pulse">
                Hello Redshell! 👋
              </h1>
              <div className="absolute -top-2 -right-2 text-2xl animate-bounce">✨</div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <span className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-300 text-sm font-semibold px-4 py-2 rounded-full flex items-center gap-2 border border-yellow-400/30">
              <Activity className="w-4 h-4 animate-pulse" />
              LIVE ANALYTICS
            </span>
            <div className="bg-white/5 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/10">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-blue-400" />
                <span className="text-white/70">Last updated:</span>
                <span className="text-white font-semibold">{dashboardData.lastUpdated}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-6 text-white/70 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-green-400" />
            <span>{dashboardData.date}</span>
          </div>
          <div className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4 text-blue-400" />
            <span>All platforms trending upward</span>
          </div>
          <div className="flex items-center gap-2">
            <Target className="w-4 h-4 text-purple-400" />
            <span>On track for monthly goals</span>
          </div>
        </div>
      </div>

      {/* Quick Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        {quickStats.map((stat, index) => (
          <div
            key={stat.label}
            className={`${stat.bg} backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:scale-105 transition-all duration-300 cursor-pointer group`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center justify-between mb-2">
              <stat.icon className={`w-5 h-5 ${stat.color} group-hover:scale-110 transition-transform`} />
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-xs text-white/60">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Enhanced Platform Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        {/* YouTube Card */}
        <div className="group relative bg-gradient-to-br from-red-900/30 to-red-800/20 backdrop-blur-sm rounded-2xl p-6 border border-red-500/20 hover:border-red-400/40 transition-all duration-500 hover:scale-105 hover:rotate-1 cursor-pointer overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Youtube className="w-10 h-10 text-red-500 group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">YouTube</h3>
                  <p className="text-red-300 text-sm">Content Hub</p>
                </div>
              </div>
              <div className="flex items-center bg-green-500/20 text-green-400 font-bold px-3 py-1 rounded-full">
                <ArrowUp className="w-4 h-4 mr-1 animate-bounce" />
                {dashboardData.youtube.growth}%
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-red-200/70 text-sm mb-1">Subscribers</p>
                <p className="text-4xl font-bold text-white group-hover:text-red-300 transition-colors">
                  {animatedNumbers.youtube.toLocaleString()}
                </p>
                <p className="text-green-400 text-sm font-semibold">{dashboardData.youtube.recentGrowth}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-red-500/20">
                <div>
                  <p className="text-red-200/70 text-xs">Videos</p>
                  <p className="text-xl font-bold text-white">{dashboardData.youtube.videos}</p>
                </div>
                <div>
                  <p className="text-red-200/70 text-xs">Total Views</p>
                  <p className="text-xl font-bold text-white">{dashboardData.youtube.totalViews}</p>
                </div>
              </div>

              <div className="bg-red-500/10 rounded-lg p-3 border border-red-500/20">
                <p className="text-red-200 text-xs mb-1">🔥 Top Performer</p>
                <p className="text-white font-semibold text-sm">{dashboardData.youtube.topVideo}</p>
              </div>
            </div>
          </div>
        </div>

        {/* TikTok Card */}
        <div className="group relative bg-gradient-to-br from-gray-900/50 to-black/30 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-500 hover:scale-105 hover:-rotate-1 cursor-pointer overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Play className="w-6 h-6 text-black" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">TikTok</h3>
                  <p className="text-gray-300 text-sm">Short Form</p>
                </div>
              </div>
              <div className="flex items-center bg-green-500/20 text-green-400 font-bold px-3 py-1 rounded-full">
                <ArrowUp className="w-4 h-4 mr-1 animate-bounce" />
                {dashboardData.tiktok.growth}%
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-gray-300/70 text-sm mb-1">Followers</p>
                <p className="text-4xl font-bold text-white group-hover:text-gray-300 transition-colors">
                  {animatedNumbers.tiktok.toLocaleString()}
                </p>
                <p className="text-green-400 text-sm font-semibold">{dashboardData.tiktok.recentGrowth}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-white/20">
                <div>
                  <p className="text-gray-300/70 text-xs">Posts</p>
                  <p className="text-xl font-bold text-white">{dashboardData.tiktok.posts}</p>
                </div>
                <div>
                  <p className="text-gray-300/70 text-xs">Total Views</p>
                  <p className="text-xl font-bold text-white">{dashboardData.tiktok.totalViews}</p>
                </div>
              </div>

              <div className="bg-white/10 rounded-lg p-3 border border-white/20">
                <p className="text-gray-200 text-xs mb-1">⚡ Trending</p>
                <p className="text-white font-semibold text-sm">{dashboardData.tiktok.topVideo}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Instagram Card */}
        <div className="group relative bg-gradient-to-br from-pink-900/30 to-purple-800/20 backdrop-blur-sm rounded-2xl p-6 border border-pink-500/20 hover:border-pink-400/40 transition-all duration-500 hover:scale-105 hover:rotate-1 cursor-pointer overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Instagram className="w-10 h-10 text-pink-500 group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">Instagram</h3>
                  <p className="text-pink-300 text-sm">Reels</p>
                </div>
              </div>
              <div className="flex items-center bg-green-500/20 text-green-400 font-bold px-3 py-1 rounded-full">
                <ArrowUp className="w-4 h-4 mr-1 animate-bounce" />
                {dashboardData.instagram.growth}%
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <p className="text-pink-200/70 text-sm mb-1">Followers</p>
                <p className="text-4xl font-bold text-white group-hover:text-pink-300 transition-colors">
                  {animatedNumbers.instagram.toLocaleString()}
                </p>
                <p className="text-green-400 text-sm font-semibold">{dashboardData.instagram.recentGrowth}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-pink-500/20">
                <div>
                  <p className="text-pink-200/70 text-xs">Posts</p>
                  <p className="text-xl font-bold text-white">{dashboardData.instagram.posts}</p>
                </div>
                <div>
                  <p className="text-pink-200/70 text-xs">Total Views</p>
                  <p className="text-xl font-bold text-white">{dashboardData.instagram.totalViews}</p>
                </div>
              </div>

              <div className="bg-pink-500/10 rounded-lg p-3 border border-pink-500/20">
                <p className="text-pink-200 text-xs mb-1">📸 Featured</p>
                <p className="text-white font-semibold text-sm">{dashboardData.instagram.topVideo}</p>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Enhanced Growth Comparison Chart */}
      <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm rounded-2xl p-8 border border-blue-500/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-white flex items-center gap-3">
              <BarChart3 className="w-6 h-6 text-blue-400" />
              Growth Comparison
            </h2>
            <p className="text-white/70 text-sm mt-1">Cross-platform follower growth trends</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm">
              <TrendingUp className="w-4 h-4" />
              All platforms growing
            </div>
          </div>
        </div>

        {/* Platform Toggle Buttons */}
        <div className="flex gap-2 mb-6 p-1 bg-white/5 rounded-lg border border-white/10">
          {['YouTube', 'TikTok', 'Instagram'].map((platform) => (
            <button
              key={platform}
              className={`flex-1 px-4 py-2 font-medium rounded-lg transition-all duration-300 ${
                activeChartTab === platform
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
              onClick={() => setActiveChartTab(platform)}
            >
              {platform}
            </button>
          ))}
        </div>

        {/* Chart Component with Enhanced Styling */}
        <div className="bg-white/5 rounded-xl p-4 border border-white/10">
          <GrowthComparisonChart activePlatform={activeChartTab} />
        </div>

        {/* Growth Insights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-blue-500/10 rounded-lg p-4 border border-blue-400/20">
            <div className="flex items-center gap-2 mb-2">
              <Youtube className="w-5 h-5 text-red-500" />
              <span className="text-white font-semibold">YouTube</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">+7.2%</div>
            <div className="text-blue-200 text-sm">Strongest growth this month</div>
          </div>

          <div className="bg-purple-500/10 rounded-lg p-4 border border-purple-400/20">
            <div className="flex items-center gap-2 mb-2">
              <Instagram className="w-5 h-5 text-pink-500" />
              <span className="text-white font-semibold">Instagram</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">+6.5%</div>
            <div className="text-purple-200 text-sm">Consistent engagement</div>
          </div>

          <div className="bg-gray-500/10 rounded-lg p-4 border border-gray-400/20">
            <div className="flex items-center gap-2 mb-2">
              <Play className="w-5 h-5 text-white" />
              <span className="text-white font-semibold">TikTok</span>
            </div>
            <div className="text-2xl font-bold text-white mb-1">+3.8%</div>
            <div className="text-gray-200 text-sm">Steady viral potential</div>
          </div>
        </div>
      </div>

      {/* Additional Dashboard Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-10">
        {/* Content Calendar Preview */}
        <div className="bg-gradient-to-br from-emerald-900/20 to-teal-900/20 backdrop-blur-sm rounded-2xl p-6 border border-emerald-500/20">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-white flex items-center gap-3">
                <Calendar className="w-6 h-6 text-emerald-400" />
                Content Calendar
              </h2>
              <p className="text-white/70 text-sm mt-1">Upcoming scheduled content</p>
            </div>
            <div className="bg-emerald-500/20 text-emerald-400 px-3 py-1 rounded-full text-sm font-semibold">
              7 scheduled
            </div>
          </div>

          <div className="space-y-3">
            {[
              { title: "Minecraft Building Tutorial", platform: "YouTube", time: "Today 3:00 PM", status: "ready" },
              { title: "Gaming Setup Tour", platform: "TikTok", time: "Tomorrow 6:00 PM", status: "editing" },
              { title: "Weekly Gaming Highlights", platform: "Instagram", time: "Friday 12:00 PM", status: "planned" }
            ].map((item, index) => (
              <div key={index} className="bg-emerald-800/20 rounded-lg p-4 border border-emerald-400/20">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-white font-semibold text-sm">{item.title}</h3>
                  <div className={`px-2 py-1 rounded-full text-xs font-bold ${
                    item.status === 'ready' ? 'bg-green-500/20 text-green-400' :
                    item.status === 'editing' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-blue-500/20 text-blue-400'
                  }`}>
                    {item.status}
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-emerald-200">{item.platform}</span>
                  <span className="text-white/60">{item.time}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Trending Topics & Alerts */}
        <div className="space-y-6">
          {/* Trending Topics */}
          <div className="bg-gradient-to-br from-purple-900/20 to-indigo-900/20 backdrop-blur-sm rounded-2xl p-6 border border-purple-500/20">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-bold text-white flex items-center gap-3">
                  <TrendingUp className="w-6 h-6 text-purple-400" />
                  Trending Topics
                </h2>
                <p className="text-white/70 text-sm mt-1">Hot topics in your niche</p>
              </div>
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            </div>

            <div className="space-y-3">
              {[
                { topic: "#MinecraftUpdate", engagement: "+245%", trend: "up" },
                { topic: "#GamingSetup2024", engagement: "+180%", trend: "up" },
                { topic: "#SpeedrunTips", engagement: "+95%", trend: "up" }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between bg-purple-800/20 rounded-lg p-3 border border-purple-400/20">
                  <div>
                    <span className="text-white font-semibold text-sm">{item.topic}</span>
                    <div className="text-purple-200 text-xs mt-1">Trending now</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-green-400 font-bold text-sm">{item.engagement}</span>
                    <TrendingUp className="w-4 h-4 text-green-400" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Alerts */}
          <div className="bg-gradient-to-br from-orange-900/20 to-red-900/20 backdrop-blur-sm rounded-2xl p-6 border border-orange-500/20">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-bold text-white flex items-center gap-3">
                  <Zap className="w-6 h-6 text-orange-400" />
                  Performance Alerts
                </h2>
                <p className="text-white/70 text-sm mt-1">Real-time notifications</p>
              </div>
              <div className="bg-orange-500/20 text-orange-400 px-2 py-1 rounded-full text-xs font-bold">
                3 new
              </div>
            </div>

            <div className="space-y-3">
              {[
                { message: "YouTube video gaining traction", type: "success", time: "2 min ago" },
                { message: "TikTok engagement below average", type: "warning", time: "1 hour ago" },
                { message: "Instagram Reels performing well", type: "success", time: "3 hours ago" }
              ].map((alert, index) => (
                <div key={index} className={`rounded-lg p-3 border ${
                  alert.type === 'success'
                    ? 'bg-green-500/10 border-green-400/20'
                    : 'bg-yellow-500/10 border-yellow-400/20'
                }`}>
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-semibold ${
                      alert.type === 'success' ? 'text-green-400' : 'text-yellow-400'
                    }`}>
                      {alert.message}
                    </span>
                    <span className="text-white/60 text-xs">{alert.time}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage; 